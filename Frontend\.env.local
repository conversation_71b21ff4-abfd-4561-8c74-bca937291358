# Supabase Public
NEXT_PUBLIC_SUPABASE_URL=https://sndusnirtpvmaebzusai.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuZHVzbmlydHB2bWFlYnp1c2FpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ0NjIzNywiZXhwIjoyMDY0MDIyMjM3fQ.1CYgNbLw6mny9-T7-pihcgeOCDw94h1FD74gRLiZk_o

# Supabase Private
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuZHVzbmlydHB2bWFlYnp1c2FpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ0NjIzNywiZXhwIjoyMDY0MDIyMjM3fQ.1CYgNbLw6mny9-T7-pihcgeOCDw94h1FD74gRLiZk_o

# Ollama
NEXT_PUBLIC_OLLAMA_URL=http://localhost:11434

# API Keys (Optional: Entering an API key here overrides the API keys globally for all users.)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GOOGLE_GEMINI_API_KEY=AIzaSyDTmKRRVF367nz6MQE1zstBL4iMS4AZF8I
MISTRAL_API_KEY=
GROQ_API_KEY=
PERPLEXITY_API_KEY=
OPENROUTER_API_KEY=

# OpenAI API Information
NEXT_PUBLIC_OPENAI_ORGANIZATION_ID=

# Azure API Information
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_GPT_35_TURBO_NAME=
AZURE_GPT_45_VISION_NAME=
AZURE_GPT_45_TURBO_NAME=
AZURE_EMBEDDINGS_NAME=

# General Configuration (Optional)
EMAIL_DOMAIN_WHITELIST=
EMAIL_WHITELIST=

# File size limit for uploads in bytes
NEXT_PUBLIC_USER_FILE_SIZE_LIMIT=10485760
