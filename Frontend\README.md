<div align="center">

# 🧠 NeuroNexus AI Chatbot

[![React](https://img.shields.io/badge/React-18.3.1-61DAFB?style=for-the-badge&logo=react&logoColor=white)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-3178C6?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-2.39.0-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)](https://supabase.io/)
[![Gemini AI](https://img.shields.io/badge/Gemini_AI-Powered-8E44AD?style=for-the-badge&logo=google&logoColor=white)](https://ai.google.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
[![Vite](https://img.shields.io/badge/Vite-5.4.2-646CFF?style=for-the-badge&logo=vite&logoColor=white)](https://vitejs.dev/)
[![License](https://img.shields.io/badge/License-ISC-blue.svg?style=for-the-badge)](https://opensource.org/licenses/ISC)

A futuristic Gemini-powered AI chatbot with 3D landing page, multilingual voice input, Google auth, dark/light mode, and chat history – built with React & TypeScript.

[✨ Live Demo](#) | [🚀 Getting Started](#-getting-started) | [📖 Documentation](#-documentation) | [🤝 Contributing](#-contributing)

![NeuroNexus AI Chatbot](https://via.placeholder.com/1200x600?text=NeuroNexus+AI+Chatbot+Screenshot)

</div>

## 📋 Table of Contents

- [✨ Features](#-features)
- [🏗️ Project Structure](#️-project-structure)
- [🚀 Getting Started](#-getting-started)
  - [Prerequisites](#prerequisites)
  - [Supabase Setup](#1-supabase-setup)
  - [Backend Setup](#2-backend-setup)
  - [Chatbot UI Setup](#3-chatbot-ui-setup)
  - [Landing Page Setup](#4-landing-page-setup)
- [🐳 Docker Deployment](#-docker-deployment)
- [🌐 Deployment](#-deployment)
- [⚙️ Additional Configuration](#️-additional-configuration)
- [❓ Troubleshooting](#-troubleshooting)
- [🔄 Continuous Integration](#-continuous-integration)
- [🤝 Contributing](#-contributing)
- [📜 License](#-license)

## ✨ Features

<div align="center">

![Features Overview](https://via.placeholder.com/800x400?text=NeuroNexus+Features)

</div>

- **🌐 Modern Landing Page**: Immersive 3D experience showcasing the chatbot's capabilities
- **🔐 Secure Authentication**: Multiple login options powered by Supabase Auth
  - Email/Password authentication
  - Single Sign-On with Google
  - Secure password recovery
- **💬 Intuitive Chat Interface**: Natural conversation flow with real-time responses
- **🗣️ Voice Input Support**: Multilingual speech recognition for hands-free interaction
- **🧠 Advanced AI**: Powered by Google's Gemini AI for human-like understanding
- **🔄 Real-time Updates**: Instant message delivery and typing indicators
- **📱 Responsive Design**: Seamless experience across all devices
- **🌓 Theme Switching**: Toggle between light and dark modes
- **📚 Conversation History**: Access and continue previous conversations
- **🔒 Privacy-Focused**: End-to-end encryption and secure data storage

## 🏗️ Project Structure

The project consists of three main components:

```
ConversAgent/
├── 🏠 project/             # Landing Page (React + Vite)
├── 💬 Chatbot/             # Chat Interface (React + TypeScript + Tailwind)
├── ⚙️ backend/             # API Server (Express + Gemini AI)
└── 🐳 docker-compose.yml   # Docker configuration
```

Each component serves a specific purpose:

- **Landing Page** (`/project`): Entry point for users with feature showcase
- **Chatbot UI** (`/Chatbot`): Main application with chat functionality
- **Backend** (`/backend`): Server that integrates with Gemini AI and Supabase

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have:

- **Node.js** (v14+)
- **npm** or **yarn**
- **Supabase** account and project
- **Google Gemini API** key

### 1. Supabase Setup

<div align="center">

![Supabase Setup](https://via.placeholder.com/600x300?text=Supabase+Setup)

</div>

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Navigate to the SQL Editor in your Supabase dashboard
3. Run the SQL schema in `/backend/supabase/schema.sql`:

```sql
-- Create profiles table
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    username TEXT,
    avatar_url TEXT,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Create messages table
CREATE TABLE public.messages (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_bot BOOLEAN DEFAULT FALSE
);

-- Set up Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile" 
    ON public.profiles FOR SELECT 
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
    ON public.profiles FOR UPDATE 
    USING (auth.uid() = id);

CREATE POLICY "Users can view their own messages" 
    ON public.messages FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own messages" 
    ON public.messages FOR INSERT 
    WITH CHECK (auth.uid() = user_id);
```

4. In the Supabase dashboard, go to Project Settings > API to get your:
   - Project URL (e.g., `https://yourproject.supabase.co`)
   - `anon` public key for the frontend
   - `service_role` secret key for the backend

### 2. Backend Setup

```bash
# Navigate to the backend directory
cd backend

# Install dependencies
npm install

# Create .env file (see example below)
touch .env

# Start the development server
npm run dev
```

**Example `.env` for backend:**

```
PORT=3001
GEMINI_API_KEY=your_gemini_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
```

### 3. Chatbot UI Setup

<div align="center">

![Chatbot UI](https://via.placeholder.com/600x300?text=Chatbot+UI+Preview)

</div>

```bash
# Navigate to the Chatbot directory
cd ../Chatbot

# Install dependencies
npm install

# Create .env file (see example below)
touch .env.local

# Start the development server
npm run dev
```

**Example `.env.local` for Chatbot UI:**

```
VITE_API_URL=http://localhost:3001/api
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Landing Page Setup

```bash
# Navigate to the project directory
cd ../project

# Install dependencies
npm install

# Start the development server
npm run dev
```

## 🐳 Docker Deployment

For simplified deployment, use Docker Compose to run all services together:

<div align="center">

![Docker Deployment](https://via.placeholder.com/600x300?text=Docker+Deployment)

</div>

**Prerequisites:**

- [Docker](https://www.docker.com/get-started) installed
- [Docker Compose](https://docs.docker.com/compose/install/) installed

**Setup:**

1. Create a `.env` file in the project root:

```env
# For Chatbot UI (frontend service)
VITE_API_URL=http://localhost:3001/api
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# For Backend (backend service)
GEMINI_API_KEY=your-gemini-api-key
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-supabase-service-key
```

2. Build and start the containers:

```bash
docker-compose up --build
```

3. Access the applications:
   - Landing Page: [http://localhost:81](http://localhost:81)
   - Chatbot UI: [http://localhost:80](http://localhost:80)
   - Backend API: Running on port 3001 (internal)

4. To stop all services:

```bash
docker-compose down
```

## 🌐 Deployment

### Backend Deployment

<details>
<summary>Click to expand deployment instructions</summary>

1. Build the backend for production:
   ```bash
   cd backend
   npm run build
   ```

2. Deploy to a Node.js hosting service (Heroku, Render, Railway, etc.)
   ```bash
   # Example for Heroku
   heroku create neuronexus-backend
   git subtree push --prefix backend heroku main
   ```

3. Set environment variables in your hosting provider's dashboard
</details>

### Chatbot UI Deployment

<details>
<summary>Click to expand deployment instructions</summary>

1. Build the Chatbot UI for production:
   ```bash
   cd Chatbot
   npm run build
   ```

2. Deploy the `dist` directory to a static hosting service:
   ```bash
   # Example for Netlify
   netlify deploy --prod --dir=dist
   ```

3. Configure environment variables in your hosting provider
</details>

### Landing Page Deployment

<details>
<summary>Click to expand deployment instructions</summary>

1. Build the landing page:
   ```bash
   cd project
   npm run build
   ```

2. Deploy the `dist` directory to a static hosting service
3. Update the "Explore Features" button to link to your deployed Chatbot UI
</details>

## ⚙️ Additional Configuration

### Customizing the Landing Page

- Modify the `LandingPage.tsx` component to match your branding
- Update color schemes in the Tailwind configuration
- Add or remove sections as needed

### Customizing AI Behavior

- Edit the conversation context in `server.js` to adjust AI personality
- Add domain-specific prompts for specialized knowledge
- Implement additional API integrations for enhanced capabilities

### Performance Optimization

- Enable caching for frequently requested information
- Implement lazy loading for components and assets
- Configure CDN for static assets

## ❓ Troubleshooting

<details>
<summary>Authentication Problems</summary>

- Verify Supabase URL and API keys are correct
- Check browser console for specific error messages
- Ensure Supabase Auth is properly configured in your project
</details>

<details>
<summary>Backend Connection Issues</summary>

- Confirm the backend server is running
- Verify the API URL in the frontend .env file
- Check CORS settings in the backend
</details>

<details>
<summary>Missing Chat History</summary>

- Verify RLS policies are properly configured
- Check database connections and permissions
- Ensure the user is authenticated before accessing messages
</details>

<details>
<summary>Gemini API Errors</summary>

- Validate your API key
- Check rate limits and quotas
- Review request formatting
</details>

## 🔄 Continuous Integration

This project is set up with CI/CD workflows for automated testing and deployment:

- **Automated Testing**: Unit and integration tests run on every pull request
- **Code Quality Checks**: ESLint and TypeScript type checking
- **Automated Deployment**: Production builds deploy automatically from the main branch

## 🤝 Contributing

Contributions are welcome! Here's how to get started:

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

Please ensure your code follows the project's coding standards and includes appropriate tests.

## 📜 License

This project is licensed under the ISC License. See the [LICENSE](LICENSE) file for details.

---

<div align="center">

**[🔝 Back to Top](#-neuronexus-ai-chatbot)**

Built with ❤️ by the NeuroNexus Team

</div>
